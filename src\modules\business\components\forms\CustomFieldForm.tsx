import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Toggle,
  Card,
  Textarea,
  ConditionalField,
  DatePicker,
  Checkbox,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { z } from 'zod';
import { useCreateCustomField, useUpdateCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail, CreateCustomFieldData } from '../../services/custom-field.service';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

interface CustomFieldFormProps {
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: CustomFieldDetail;
  isSubmitting?: boolean;
}

// Định nghĩa các pattern phổ biến với key để đa ngôn ngữ
const COMMON_PATTERN_KEYS = [
  { key: 'email', value: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
  { key: 'phoneVN', value: '^(\\+84|0)[0-9]{9,10}$' },
  { key: 'phoneIntl', value: '^\\+[1-9]\\d{1,14}$' },
  { key: 'postalCodeVN', value: '^[0-9]{5,6}$' },
  { key: 'lettersOnly', value: '^[a-zA-Z]+$' },
  { key: 'numbersOnly', value: '^[0-9]+$' },
  { key: 'alphanumeric', value: '^[a-zA-Z0-9]+$' },
  { key: 'noSpecialChars', value: '^[a-zA-Z0-9\\s]+$' },
  { key: 'url', value: '^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$' },
  { key: 'ipv4', value: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$' },
  { key: 'strongPassword', value: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$' },
  { key: 'vietnameseName', value: '^[a-zA-ZÀ-ỹ\\s]+$' },
  { key: 'studentId', value: '^[A-Z]{2}[0-9]{6}$' },
  { key: 'nationalId', value: '^[0-9]{9,12}$' },
  { key: 'taxCode', value: '^[0-9]{10,13}$' },
  { key: 'dateFormat', value: '^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[012])\\/(19|20)\\d\\d$' },
  { key: 'timeFormat', value: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
  { key: 'hexColor', value: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$' },
  { key: 'base64', value: '^[A-Za-z0-9+\\/]*={0,2}$' },
  { key: 'uuid', value: '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' },
  { key: 'filename', value: '^[^<>:"/\\\\|?*]+\\.[a-zA-Z0-9]+$' },
  { key: 'urlSlug', value: '^[a-z0-9]+(?:-[a-z0-9]+)*$' },
  { key: 'variableName', value: '^[a-zA-Z_$][a-zA-Z0-9_$]*$' },
  { key: 'creditCard', value: '^[0-9]{13,19}$' },
  { key: 'qrCode', value: '^[A-Z0-9 $%*+\\-./:]+$' },
  { key: 'gpsCoordinate', value: '^-?([1-8]?[1-9]|[1-9]0)\\.{1}\\d{1,6}$' },
  { key: 'rgbColor', value: '^rgb\\(([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\)$' },
  { key: 'domain', value: '^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$' },
  { key: 'decimal', value: '^\\d+(\\.\\d{1,2})?$' },
  { key: 'barcode', value: '^[0-9]{8,14}$' },
];

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: createCustomField, isPending: isCreating } = useCreateCustomField();
  const { mutateAsync: updateCustomField, isPending: isUpdating } = useUpdateCustomField();

  const formRef = useRef<FormRef<Record<string, unknown>> | null>(null);
  const patternInputRef = useRef<HTMLInputElement>(null);

  // State cho advanced settings
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Schema cho form
  const customFieldSchema = z.object({
    id: z.string().min(1, t('business:customField.form.idRequired')),
    displayName: z.string().min(1, t('business:customField.form.displayNameRequired')),
    label: z.string().optional(), // Nhãn không bắt buộc
    type: z.string().min(1, t('business:customField.form.typeRequired')),
    placeholder: z.string().optional(),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
    validation: z.object({
      minLength: z.string().optional(),
      maxLength: z.string().optional(),
      pattern: z.string().optional(),
    }).optional(),
    options: z.string().optional(),
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    id: '',
    displayName: '',
    type: 'text',
    placeholder: '',
    defaultValue: '',
    description: '',
    validation: {
      minLength: '',
      maxLength: '',
      pattern: '',
    },
    options: '',
  };

  // Định nghĩa kiểu dữ liệu cho form values
  type CustomFieldFormValues = z.infer<typeof customFieldSchema>;

  // Xử lý khi submit form
  const handleSubmit = async (values: CustomFieldFormValues) => {
    try {
      // Chuẩn bị dữ liệu cho API - displayName sẽ được truyền xuống backend là label
      const formData: CreateCustomFieldData = {
        component: 'input', // Mặc định là input vì đã bỏ trường component
        config: {
          id: String(values.id),
          label: String(values.displayName), // displayName truyền xuống backend là label
          displayName: String(values.displayName),
          type: String(values.type),
          required: false, // Mặc định không bắt buộc
          placeholder: values.placeholder ? String(values.placeholder) : undefined,
          defaultValue: values.defaultValue ? String(values.defaultValue) : undefined,
          description: values.description ? String(values.description) : undefined,
          validation: {
            minLength: values.validation && values.validation.minLength ? Number(values.validation.minLength) : undefined,
            maxLength: values.validation && values.validation.maxLength ? Number(values.validation.maxLength) : undefined,
            pattern: values.validation && values.validation.pattern ? String(values.validation.pattern) : undefined,
          },
        },
      };

      // Thêm options nếu có (chỉ áp dụng cho select, radio, checkbox)
      if (values.options) {
        try {
          // Parse options từ chuỗi JSON hoặc từ danh sách các giá trị ngăn cách bởi dấu phẩy
          const optionsString = String(values.options);
          const optionsArray = optionsString.includes('{')
            ? JSON.parse(optionsString)
            : optionsString.split(',').map(option => ({
                label: option.trim(),
                value: option.trim().toLowerCase().replace(/\s+/g, '_'),
              }));

          formData.config.options = optionsArray;
        } catch (error) {
          console.error('Error parsing options:', error);
        }
      }

      if (initialData) {
        // Cập nhật trường tùy chỉnh
        await updateCustomField({
          id: initialData.id,
          data: formData,
        });
      } else {
        // Tạo trường tùy chỉnh mới
        await createCustomField(formData);
      }

      // Gọi callback onSubmit
      onSubmit();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };



  // Chuẩn bị giá trị mặc định từ dữ liệu ban đầu
  const getInitialValues = () => {
    if (!initialData) return defaultValues;

    // Lấy dữ liệu validation từ configJson
    const validation = initialData.configJson?.validation as Record<string, unknown> | undefined;

    return {
      id: initialData.configJson?.id as string || '',
      displayName: initialData.configJson?.displayName as string || initialData.label || '',
      type: initialData.type,
      placeholder: initialData.configJson?.placeholder as string || '',
      defaultValue: initialData.configJson?.defaultValue as string || '',
      description: initialData.configJson?.description as string || '',
      validation: {
        minLength: validation?.minLength ? String(validation.minLength) : '',
        maxLength: validation?.maxLength ? String(validation.maxLength) : '',
        pattern: validation?.pattern ? String(validation.pattern) : '',
      },
      options: initialData.configJson?.options
        ? JSON.stringify(initialData.configJson.options)
        : '',
    };
  };

  return (
    <Card title={initialData ? t('business:customField.edit') : t('business:customField.add')}>
      <Form
        ref={formRef}
        schema={customFieldSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={getInitialValues()}
        className="p-4 space-y-6"
      >
        {/* Thông tin cơ bản */}
        <div className="space-y-4 mb-6">
          <FormItem
            name="type"
            label={t('business:customField.type')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'text', label: t('business:customField.types.text') },
                { value: 'number', label: t('business:customField.types.number') },
                { value: 'boolean', label: t('business:customField.types.boolean') },
                { value: 'date', label: t('business:customField.types.date') },
                { value: 'select', label: t('business:customField.types.select') },
                { value: 'object', label: t('business:customField.types.object') },
                { value: 'array', label: t('business:customField.types.array') },
              ]}
            />
          </FormItem>

          <FormItem
            name="id"
            label={t('business:customField.form.fieldIdLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:customField.form.fieldIdPlaceholder')}
              pattern="^[a-zA-Z0-9_-]+$"
            />
          </FormItem>

          <FormItem
            name="displayName"
            label={t('business:customField.form.displayNameLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:customField.form.displayNamePlaceholder')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('business:customField.form.description')}
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('business:customField.form.descriptionPlaceholder')}
            />
          </FormItem>

          {/* Tùy chọn cho kiểu dữ liệu select */}
          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.EQUALS,
              value: 'select',
            }}
          >
            <FormItem
              name="options"
              label={t('business:customField.form.options')}
            >
              <Textarea
                fullWidth
                rows={6}
                placeholder={t('business:customField.form.selectOptionsPlaceholder')}
              />
            </FormItem>
          </ConditionalField>
        </div>

        {/* Checkbox để hiện/ẩn cài đặt nâng cao */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            checked={showAdvancedSettings}
            onChange={setShowAdvancedSettings}
            label={t('business:customField.form.showAdvancedSettings', 'Hiển thị cài đặt nâng cao')}
          />
        </div>

        {/* Cài đặt nâng cao */}
        {showAdvancedSettings && (
          <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <FormItem
              name="placeholder"
              label={t('business:customField.form.placeholder')}
            >
              <Input fullWidth placeholder={t('business:customField.form.placeholderPlaceholder')} />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'number',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  type="number"
                  placeholder={t('business:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'boolean',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <Select
                  fullWidth
                  placeholder={t('business:customField.form.booleanDefaultPlaceholder')}
                  options={[
                    { value: 'true', label: t('business:customField.booleanValues.true') },
                    { value: 'false', label: t('business:customField.booleanValues.false') },
                  ]}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'date',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <DatePicker
                  fullWidth
                  placeholder={t('business:customField.form.dateDefaultPlaceholder')}
                  format="dd/MM/yyyy"
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.IN,
                value: ['text', 'select', 'object', 'array'],
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  placeholder={t('business:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>





            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Validation cho Text */}
              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.minLength"
                  label={t('business:customField.validation.minLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="0" />
                </FormItem>
              </ConditionalField>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.maxLength"
                  label={t('business:customField.validation.maxLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="100" />
                </FormItem>
              </ConditionalField>

              {/* Validation cho Number */}
              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'number',
                }}
              >
                <FormItem
                  name="validation.min"
                  label={t('business:customField.validation.min')}
                >
                  <Input fullWidth type="number" placeholder="0" />
                </FormItem>
              </ConditionalField>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'number',
                }}
              >
                <FormItem
                  name="validation.max"
                  label={t('business:customField.validation.max')}
                >
                  <Input fullWidth type="number" placeholder="100" />
                </FormItem>
              </ConditionalField>
            </div>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.pattern"
                  label={t('business:customField.validation.pattern')}
                >
                  <div className="space-y-2">
                    <Input
                      ref={patternInputRef}
                      fullWidth
                      placeholder="^[A-Za-z0-9]+$"
                    />
                    <div className="text-sm text-gray-600">
                      <p className="font-medium mb-2">{t('business:customField.form.patternSuggestions')}</p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                        {COMMON_PATTERN_KEYS.map((pattern, index) => (
                          <button
                            key={index}
                            type="button"
                            className="text-left text-xs p-1 hover:bg-gray-100 rounded truncate"
                            title={pattern.value}
                            onClick={() => {
                              if (patternInputRef.current && formRef.current) {
                                // Cập nhật giá trị input
                                patternInputRef.current.value = pattern.value;
                                // Cập nhật form value
                                formRef.current.setValues({
                                  'validation.pattern': pattern.value
                                });
                                // Trigger change event
                                patternInputRef.current.dispatchEvent(new Event('input', { bubbles: true }));
                              }
                            }}
                          >
                            <span className="font-medium text-blue-600">{t(`business:customField.patterns.${pattern.key}`)}:</span>
                            <span className="ml-1 text-gray-500 font-mono">{pattern.value.substring(0, 20)}...</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </FormItem>
              </ConditionalField>


          </div>
        )}

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isCreating || isUpdating}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isCreating || isUpdating}
          >
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
