import React, { useState, ReactNode } from 'react';
import { Card, Icon } from './index';
import { cn } from '@/shared/utils/cn';

export interface CollapsibleCardProps {
  /**
   * Tiêu đề của card
   */
  title: ReactNode;

  /**
   * Nội dung của card
   */
  children: ReactNode;

  /**
   * Class bổ sung cho card
   */
  className?: string;

  /**
   * Trạng thái mở ban đầu (chỉ dùng khi không có isOpen)
   */
  defaultOpen?: boolean;

  /**
   * Trạng thái mở (controlled mode)
   */
  isOpen?: boolean;

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;

  /**
   * Có lazy load content không (chỉ render children khi mở)
   */
  lazyLoad?: boolean;
}

/**
 * Component Card có thể đóng/mở
 */
const CollapsibleCard: React.FC<CollapsibleCardProps> = ({
  title,
  children,
  className,
  defaultOpen = false,
  isOpen: controlledIsOpen,
  onToggle,
  lazyLoad = false,
}) => {
  const [internalIsOpen, setInternalIsOpen] = useState(defaultOpen);

  // Sử dụng controlled mode nếu có isOpen prop, ngược lại dùng internal state
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;
  const [hasBeenOpened, setHasBeenOpened] = useState(isOpen);

  const handleToggle = () => {
    const newState = !isOpen;

    if (controlledIsOpen === undefined) {
      setInternalIsOpen(newState);
    }

    if (newState && !hasBeenOpened) {
      setHasBeenOpened(true);
    }

    if (onToggle) {
      onToggle(newState);
    }
  };

  return (
    <Card className={cn('relative transition-all duration-300', className)} style={{ zIndex: 1 }}>
      <div className="flex items-center justify-between p-1 cursor-pointer" onClick={handleToggle}>
        <div className="flex-1">{title}</div>
        <div className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
          <Icon
            name={isOpen ? 'chevron-up' : 'chevron-down'}
            className="text-gray-500 transition-transform duration-300"
          />
        </div>
      </div>

      <div
        className={cn(
          'transition-all duration-300 overflow-hidden',
          isOpen ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="p-4 pt-4 border-t border-gray-100 dark:border-gray-800 relative" style={{ zIndex: 10 }}>
          {lazyLoad ? (hasBeenOpened ? children : null) : children}
        </div>
      </div>
    </Card>
  );
};

export default CollapsibleCard;
