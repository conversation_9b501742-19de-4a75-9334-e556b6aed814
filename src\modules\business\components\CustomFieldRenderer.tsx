import React, { useState, useEffect, useCallback } from 'react';
import {
  Input,
  Textarea,
  Select,
  Checkbox,
  Typography,
  DatePicker,
} from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { useTranslation } from 'react-i18next';

interface CustomFieldRendererProps {
  field: {
    id: number;
    fieldId: number;
    label: string;
    component: string;
    type: string;
    required: boolean;
    configJson: Record<string, unknown>;
    value: Record<string, unknown>;
  };
  value: string | number | boolean;
  onChange: (value: string | number | boolean) => void;
  onRemove: () => void;
}

/**
 * Component để render custom field dựa trên type
 */
const CustomFieldRenderer: React.FC<CustomFieldRendererProps> = ({
  field,
  value,
  onChange,
  onRemove,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const [error, setError] = useState<string>('');

  // Lấy config từ configJson
  const config = field.configJson || {};
  const placeholder = (config as Record<string, unknown>)?.placeholder as string || t('business:product.form.customFields.valuePlaceholder', 'Nhập giá trị');
  const options = (config as Record<string, unknown>)?.options as Array<{ label: string; value: string | number | boolean }> || [];

  // Lấy validation config
  const validation = (config as Record<string, unknown>)?.validation as Record<string, unknown> || {};
  const minLength = validation.minLength as number;
  const maxLength = validation.maxLength as number;
  const pattern = validation.pattern as string;
  const min = validation.min as number;
  const max = validation.max as number;

  // Validation function
  const validateValue = useCallback((val: string | number | boolean): string => {
    const stringValue = String(val || '');

    // Required validation
    if (field.required && (!val || stringValue.trim() === '')) {
      return t('business:product.form.customFields.validation.required', 'Trường này là bắt buộc');
    }

    // Skip other validations if value is empty and not required
    if (!val || stringValue.trim() === '') {
      return '';
    }

    const componentType = (field.component || field.type || 'text').toLowerCase();

    // Text validations
    if (componentType === 'text' || componentType === 'textarea' || componentType === 'email' || componentType === 'url') {
      if (minLength && stringValue.length < minLength) {
        return t('business:product.form.customFields.validation.minLength', 'Tối thiểu {{min}} ký tự', { min: minLength });
      }
      if (maxLength && stringValue.length > maxLength) {
        return t('business:product.form.customFields.validation.maxLength', 'Tối đa {{max}} ký tự', { max: maxLength });
      }
      if (pattern) {
        const regex = new RegExp(pattern);
        if (!regex.test(stringValue)) {
          return t('business:product.form.customFields.validation.pattern', 'Định dạng không hợp lệ');
        }
      }
    }

    // Number validations
    if (componentType === 'number') {
      const numValue = Number(val);
      if (isNaN(numValue)) {
        return t('business:product.form.customFields.validation.invalidNumber', 'Phải là số hợp lệ');
      }
      if (min !== undefined && numValue < min) {
        return t('business:product.form.customFields.validation.min', 'Tối thiểu {{min}}', { min });
      }
      if (max !== undefined && numValue > max) {
        return t('business:product.form.customFields.validation.max', 'Tối đa {{max}}', { max });
      }
    }

    // Email validation
    if (componentType === 'email') {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(stringValue)) {
        return t('business:product.form.customFields.validation.invalidEmail', 'Email không hợp lệ');
      }
    }

    // URL validation
    if (componentType === 'url') {
      try {
        new URL(stringValue);
      } catch {
        return t('business:product.form.customFields.validation.invalidUrl', 'URL không hợp lệ');
      }
    }

    // Date validation
    if (componentType === 'date') {
      if (stringValue && isNaN(Date.parse(stringValue))) {
        return t('business:product.form.customFields.validation.invalidDate', 'Ngày không hợp lệ');
      }
    }

    return '';
  }, [field.required, field.component, field.type, minLength, maxLength, pattern, min, max, t]);

  // Validate on value change
  useEffect(() => {
    const errorMessage = validateValue(value);
    setError(errorMessage);
  }, [value, field.required, minLength, maxLength, pattern, min, max, validateValue]);

  // Handle value change with validation
  const handleChange = (newValue: string | number | boolean) => {
    onChange(newValue);
    const errorMessage = validateValue(newValue);
    setError(errorMessage);
  };

  // Render component dựa trên type
  const renderInput = () => {
    const componentType = (field.component || field.type || 'text').toLowerCase();
    switch (componentType) {
      case 'textarea':
        return (
          <div>
            <Textarea
              fullWidth
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              rows={3}
              error={!!error}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'number':
        return (
          <div>
            <Input
              fullWidth
              type="number"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              error={!!error}
              min={min}
              max={max}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'email':
        return (
          <div>
            <Input
              fullWidth
              type="email"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              error={!!error}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'phone':
        return (
          <div>
            <Input
              fullWidth
              type="tel"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              error={!!error}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'url':
        return (
          <div>
            <Input
              fullWidth
              type="url"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              error={!!error}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'password':
        return (
          <Input
            fullWidth
            type="password"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'date':
        return (
          <div>
            <DatePicker
              fullWidth
              value={value ? (() => {
                try {
                  return new Date(value as string);
                } catch {
                  return null;
                }
              })() : null}
              onChange={(date) => {
                const dateValue = date ? date.toISOString().split('T')[0] : '';
                handleChange(dateValue);
              }}
              placeholder={placeholder}
              format="dd/MM/yyyy"
              error={!!error}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'time':
        return (
          <Input
            fullWidth
            type="time"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'datetime':
        return (
          <Input
            fullWidth
            type="datetime-local"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'color':
        return (
          <Input
            fullWidth
            type="color"
            value={value as string || '#000000'}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'range':
        return (
          <Input
            fullWidth
            type="range"
            value={value as string || '0'}
            onChange={(e) => onChange(e.target.value)}
            min={(config as Record<string, unknown>)?.min as number || 0}
            max={(config as Record<string, unknown>)?.max as number || 100}
          />
        );

      case 'select':
        return (
          <Select
            fullWidth
            value={value as string || ''}
            onChange={(val) => onChange(val as string)}
            options={options.map((opt) => ({
              value: String(opt.value),
              label: opt.label,
            }))}
            placeholder={placeholder}
          />
        );

      case 'checkbox':
        return (
          <Checkbox
            checked={Boolean(value)}
            onChange={(checked) => onChange(checked)}
            label={placeholder}
          />
        );

      case 'switch':
        return (
          <Toggle
            checked={Boolean(value)}
            onChange={(checked: boolean) => onChange(checked)}
            label={placeholder}
          />
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {options.map((option, index: number) => (
              <label key={index} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={`radio-${field.id}`}
                  value={String(option.value)}
                  checked={value === option.value}
                  onChange={(e) => onChange(e.target.value)}
                  className="form-radio"
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'text':
      default:
        return (
          <div>
            <Input
              fullWidth
              type="text"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              error={!!error}
              minLength={minLength}
              maxLength={maxLength}
              pattern={pattern}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );
    }
  };

  return (
    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Typography variant="caption" className="font-medium">
            {field.label}
          </Typography>
          {field.required && (
            <span className="text-red-500 text-xs">*</span>
          )}
          <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
            {field.component || field.type || 'text'}
          </span>
        </div>
        <button
          type="button"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700 text-sm"
        >
          ✕
        </button>
      </div>
      {renderInput()}
    </div>
  );
};

export default CustomFieldRenderer;
